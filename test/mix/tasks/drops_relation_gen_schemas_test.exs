defmodule Mix.Tasks.Drops.Relation.GenSchemasTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.Schema.Generator

  describe "GenSchemas task" do
    test "generates schema content without duplicated defmodule" do
      # Test the Generator directly to verify the fix
      field = Drops.Relation.Schema.Field.new(:name, :string, %{source: :name})
      schema = Drops.Relation.Schema.new("users", nil, [], [field], [])

      # Generate schema content as string
      ast = Generator.generate_module_content("TestApp.Relations.User", "users", schema)
      schema_content = Macro.to_string(ast)

      # Verify there's only one defmodule statement
      defmodule_count =
        schema_content
        |> String.split("\n")
        |> Enum.count(&String.contains?(&1, "defmodule"))

      assert defmodule_count == 1, "Expected exactly 1 defmodule, got #{defmodule_count}"

      # Verify the content structure
      assert schema_content =~ "defmodule TestApp.Relations.User do"
      assert schema_content =~ "use Ecto.Schema"
      assert schema_content =~ "import Ecto.Schema"
      assert schema_content =~ "schema(\"users\")"
      assert schema_content =~ "field(:name, :string)"
      assert schema_content =~ "timestamps()"
      assert schema_content =~ "end"

      # Verify it doesn't contain nested defmodule
      refute schema_content =~ ~r/defmodule.*defmodule/s
    end

    test "generated schema content can be compiled successfully" do
      # Test that the generated content is valid Elixir code
      field = Drops.Relation.Schema.Field.new(:email, :string, %{source: :email})
      schema = Drops.Relation.Schema.new("users", nil, [], [field], [])

      # Generate a unique module name to avoid conflicts
      timestamp = System.system_time(:nanosecond)
      module_name = "TestApp.Relations.CompileTest#{timestamp}"

      # Generate and compile the schema
      ast = Generator.generate_module_content(module_name, "users", schema)

      # This should not raise any compilation errors
      assert {_result, _bindings} = Code.eval_quoted(ast)

      # Verify the module was created and works
      module = String.to_existing_atom(module_name)
      assert module.__schema__(:source) == "users"
      assert :email in module.__schema__(:fields)
    end
  end
end
